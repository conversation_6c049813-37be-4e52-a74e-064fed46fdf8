// PDF Form Builder JavaScript
class PDFFormBuilder {
    constructor() {
        this.pdfDoc = null;
        this.canvas = null;
        this.ctx = null;
        this.fields = new Map();
        this.selectedField = null;
        this.zoom = 1.0;
        this.gridEnabled = false;
        this.pdfScale = 1.0;
        this.pdfOffset = { x: 0, y: 0 };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createCanvas();
    }

    setupEventListeners() {
        // PDF file input
        document.getElementById('pdf-file').addEventListener('change', (e) => {
            if (e.target.files[0]) {
                this.loadPDF(e.target.files[0]);
            }
        });

        // Config file input (will be set up after DOM is ready)
        setTimeout(() => {
            const configFile = document.getElementById('config-file');
            if (configFile) {
                configFile.addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        this.loadConfig(e.target.files[0]);
                    }
                });
            }
        }, 100);

        // Keyboard event listeners for arrow key movement
        document.addEventListener('keydown', (e) => {
            this.onKeyDown(e);
        });

        // Prevent default arrow key behavior when form builder is focused
        document.addEventListener('keydown', (e) => {
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key) && this.selectedField) {
                e.preventDefault();
            }
        });
    }

    createCanvas() {
        const container = document.getElementById('pdf-container');
        container.innerHTML = `
            <canvas id="pdf-canvas" style="display: none;"></canvas>
            <div class="loading">📄 Load a PDF file to start creating form fields</div>
        `;

        this.canvas = document.getElementById('pdf-canvas');
        this.ctx = this.canvas.getContext('2d');

        // Reset container sizing to default
        container.style.minWidth = '';
        container.style.minHeight = '';

        // Canvas event listeners
        this.canvas.addEventListener('click', (e) => this.onCanvasClick(e));
        this.canvas.addEventListener('mousemove', (e) => this.onCanvasMouseMove(e));

        // Drag and drop listeners for the PDF container
        const container1 = document.getElementById('pdf-container');
        container1.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });

        container1.addEventListener('drop', (e) => {
            e.preventDefault();
            const fieldId = e.dataTransfer.getData('text/plain');
            if (fieldId) {
                this.onFieldDrop(e, fieldId);
            }
        });
    }

    async loadPDF(file) {
        try {
            const arrayBuffer = await file.arrayBuffer();

            // Load PDF using PDF.js (we'll need to include this library)
            const loadingTask = pdfjsLib.getDocument(arrayBuffer);
            this.pdfDoc = await loadingTask.promise;

            await this.renderPDF();
            this.showStatus('PDF loaded successfully!', 'success');
        } catch (error) {
            this.showStatus(`Error loading PDF: ${error.message}`, 'error');
        }
    }

    async loadDefaultPDF() {
        try {
            const response = await fetch('./OG_docs/BLANK OP REPORT.pdf');
            if (!response.ok) {
                throw new Error('Default PDF not found');
            }

            const arrayBuffer = await response.arrayBuffer();
            const loadingTask = pdfjsLib.getDocument(arrayBuffer);
            this.pdfDoc = await loadingTask.promise;

            await this.renderPDF();
            this.showStatus('Default PDF loaded successfully!', 'success');
        } catch (error) {
            this.showStatus(`Error loading default PDF: ${error.message}`, 'error');
        }
    }

    async renderPDF() {
        if (!this.pdfDoc) return;

        const page = await this.pdfDoc.getPage(1);
        const viewport = page.getViewport({ scale: this.zoom });

        // Set canvas dimensions to match PDF viewport
        this.canvas.width = viewport.width;
        this.canvas.height = viewport.height;
        this.canvas.style.display = 'block';

        // Ensure container size matches canvas size
        this.syncContainerWithCanvas();

        // Clear loading message
        const loading = document.querySelector('.loading');
        if (loading) loading.style.display = 'none';

        const renderContext = {
            canvasContext: this.ctx,
            viewport: viewport
        };

        await page.render(renderContext).promise;

        // Update scale and offset for coordinate calculations
        this.pdfScale = viewport.scale;
        this.updatePDFOffset();

        // Redraw field markers
        this.redrawFieldMarkers();

        // Draw grid if enabled
        if (this.gridEnabled) {
            this.drawGrid();
        }
    }

    syncContainerWithCanvas() {
        if (!this.canvas) return;

        const container = document.getElementById('pdf-container');
        if (!container) return;

        // Calculate the required container size to fit the canvas plus padding
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        // Add some padding around the canvas (same as CSS padding: 2rem = 32px)
        const padding = 32;
        const totalWidth = canvasWidth + (padding * 2);
        const totalHeight = canvasHeight + (padding * 2);

        // Set container dimensions to match canvas + padding
        container.style.minWidth = `${totalWidth}px`;
        container.style.minHeight = `${totalHeight}px`;

        // Position canvas at top-left with padding instead of centering
        // This ensures field markers align correctly with canvas coordinates
        this.canvas.style.margin = `${padding}px`;
        this.canvas.style.display = 'block';

        console.log(`📐 Container synced: Canvas(${canvasWidth}x${canvasHeight}) → Container(${totalWidth}x${totalHeight})`);
    }

    updatePDFOffset() {
        const rect = this.canvas.getBoundingClientRect();
        const pdfContainer = document.getElementById('pdf-container');
        const containerRect = pdfContainer.getBoundingClientRect();

        // Calculate offset considering the canvas padding
        this.pdfOffset.x = rect.left - containerRect.left;
        this.pdfOffset.y = rect.top - containerRect.top;

        console.log(`📍 PDF Offset updated: x=${this.pdfOffset.x}, y=${this.pdfOffset.y}`);
    }

    onCanvasClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Convert to PDF coordinates (PDF coordinate system has origin at bottom-left)
        // Important: Use zoom level to get actual PDF coordinates
        const pdfX = Math.round(x / this.zoom);
        const pdfY = Math.round((this.canvas.height / this.zoom) - (y / this.zoom));

        console.log(`🎯 Click at canvas (${x}, ${y}) → PDF (${pdfX}, ${pdfY}) [zoom: ${this.zoom}]`);

        this.createField(pdfX, pdfY, x, y);
    }

    onCanvasMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Convert to PDF coordinates using zoom level
        const pdfX = Math.round(x / this.zoom);
        const pdfY = Math.round((this.canvas.height / this.zoom) - (y / this.zoom));

        document.getElementById('mouse-coords').textContent = `x: ${pdfX}, y: ${pdfY} (zoom: ${Math.round(this.zoom * 100)}%)`;
    }

    onKeyDown(e) {
        // Only handle arrow keys when a field is selected
        if (!this.selectedField) return;

        const field = this.fields.get(this.selectedField);
        if (!field) return;

        // Determine movement step size based on modifier keys
        let stepSize = 1; // Default: 1 PDF unit
        if (e.shiftKey) stepSize = 10; // Shift: 10 PDF units (coarse movement)
        if (e.ctrlKey || e.metaKey) stepSize = 0.5; // Ctrl/Cmd: 0.5 PDF units (fine movement)

        let deltaX = 0;
        let deltaY = 0;

        switch (e.key) {
            case 'ArrowLeft':
                deltaX = -stepSize;
                break;
            case 'ArrowRight':
                deltaX = stepSize;
                break;
            case 'ArrowUp':
                deltaY = stepSize; // PDF coordinates: up = positive Y
                break;
            case 'ArrowDown':
                deltaY = -stepSize; // PDF coordinates: down = negative Y
                break;
            default:
                return; // Not an arrow key
        }

        // Update field coordinates
        field.x += deltaX;
        field.y += deltaY;

        // Ensure coordinates don't go negative
        field.x = Math.max(0, field.x);
        field.y = Math.max(0, field.y);

        // Update marker position
        this.updateFieldMarkerPosition(field);

        // Update field editor if this field is selected
        this.showFieldEditor(field);

        // Update field list
        this.updateFieldList();

        // Show status with movement info
        const modifierText = e.shiftKey ? ' (coarse)' : e.ctrlKey || e.metaKey ? ' (fine)' : '';
        this.showStatus(`Moved "${field.name}" to x: ${field.x}, y: ${field.y}${modifierText}`);

        console.log(`⌨️ Keyboard move: ${e.key} (step: ${stepSize}) → PDF(${field.x}, ${field.y})`);
    }

    onFieldDrop(e, fieldId) {
        const field = this.fields.get(fieldId);
        if (!field) return;

        const canvasRect = this.canvas.getBoundingClientRect();

        // Calculate drop position relative to canvas
        const x = e.clientX - canvasRect.left;
        const y = e.clientY - canvasRect.top;

        // Convert to PDF coordinates using zoom level
        const pdfX = Math.round(x / this.zoom);
        const pdfY = Math.round((this.canvas.height / this.zoom) - (y / this.zoom));

        console.log(`🎯 Drag drop at canvas (${x}, ${y}) → PDF (${pdfX}, ${pdfY}) [zoom: ${this.zoom}]`);

        // Update field coordinates
        field.x = pdfX;
        field.y = pdfY;
        field.canvasX = x;
        field.canvasY = y;

        // Update marker position
        const marker = document.getElementById(`marker_${fieldId}`);
        if (marker) {
            // Calculate text height based on font size
            const textHeight = field.size * 0.8;

            // Position marker relative to container, accounting for canvas offset
            const markerX = this.pdfOffset.x + x;
            const markerY = this.pdfOffset.y + y - (textHeight / 2);

            marker.style.left = `${markerX}px`;
            marker.style.top = `${markerY}px`;
            marker.style.height = `${textHeight}px`;
        }

        // Update field editor if this field is selected
        if (this.selectedField === fieldId) {
            this.showFieldEditor(field);
        }

        // Update field list
        this.updateFieldList();

        this.showStatus(`Field "${field.name}" moved to x: ${pdfX}, y: ${pdfY}`);
    }

    createField(pdfX, pdfY, canvasX, canvasY) {
        const fieldId = `field_${Date.now()}`;
        const field = {
            id: fieldId,
            name: `field_${this.fields.size + 1}`,
            x: pdfX,
            y: pdfY,
            canvasX: canvasX,
            canvasY: canvasY,
            size: 10,
            pageIndex: 0,
            bold: false,
            maxWidth: null,
            lineSpacing: null,
            maxLines: null,
            type: 'text'
        };

        this.fields.set(fieldId, field);
        this.selectField(fieldId);
        this.updateFieldList();
        this.createFieldMarker(field);
        this.showFieldEditor(field);
    }

    createFieldMarker(field) {
        const marker = document.createElement('div');
        marker.className = 'field-marker';
        marker.id = `marker_${field.id}`;

        // Calculate marker height based on font size (approximate text height)
        const textHeight = field.size * 0.8; // Approximate text height based on font size

        // Position marker relative to container, accounting for canvas offset
        // X position stays at left edge, Y position adjusted to be at middle-left of text
        const markerX = this.pdfOffset.x + field.canvasX;
        const markerY = this.pdfOffset.y + field.canvasY - (textHeight / 2);

        marker.style.left = `${markerX}px`;
        marker.style.top = `${markerY}px`;
        marker.style.width = '4px';
        marker.style.height = `${textHeight}px`;
        marker.style.borderRadius = '0';
        marker.draggable = true;

        // Add field name label
        const label = document.createElement('div');
        label.className = 'field-label';
        label.textContent = field.name;
        label.style.cssText = `
            position: absolute;
            top: -25px;
            left: 0;
            transform: translateY(-100%);
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        `;
        marker.appendChild(label);

        // Show label on hover
        marker.addEventListener('mouseenter', () => {
            label.style.opacity = '1';
        });

        marker.addEventListener('mouseleave', () => {
            label.style.opacity = '0';
        });

        marker.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectField(field.id);
        });

        // Drag functionality
        marker.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', field.id);
            marker.style.opacity = '0.5';
            this.selectField(field.id);
        });

        marker.addEventListener('dragend', (e) => {
            marker.style.opacity = '1';
        });

        const pdfContainer = document.getElementById('pdf-container');
        pdfContainer.appendChild(marker);
    }

    selectField(fieldId) {
        // Remove previous selection
        document.querySelectorAll('.field-marker').forEach(marker => {
            marker.classList.remove('selected');
        });

        document.querySelectorAll('.field-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Add selection to current field
        const marker = document.getElementById(`marker_${fieldId}`);
        if (marker) marker.classList.add('selected');

        const listItem = document.getElementById(`item_${fieldId}`);
        if (listItem) listItem.classList.add('selected');

        this.selectedField = fieldId;
        const field = this.fields.get(fieldId);
        if (field) {
            this.showFieldEditor(field);
        }
    }

    showFieldEditor(field) {
        const editor = document.getElementById('field-editor');
        editor.innerHTML = `
            <div style="background: #e8f5e8; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem; border-left: 4px solid #27ae60;">
                <small style="color: #1e7e34;">
                    ✅ <strong>Field Selected:</strong> ${field.name}<br>
                    Use <kbd>↑↓←→</kbd> keys to move this field
                </small>
            </div>
            <div class="form-group">
                <label>Field Name:</label>
                <input type="text" id="field-name" value="${field.name}" onchange="formBuilder.updateField('name', this.value)">
            </div>
            <div class="form-group">
                <label>Type:</label>
                <select id="field-type" onchange="formBuilder.updateField('type', this.value)">
                    <option value="text" ${field.type === 'text' ? 'selected' : ''}>Text</option>
                    <option value="checkbox" ${field.type === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                    <option value="array" ${field.type === 'array' ? 'selected' : ''}>Array/List</option>
                </select>
            </div>
            <div class="form-group">
                <label>X Position:</label>
                <input type="number" id="field-x" value="${field.x}" onchange="formBuilder.updateField('x', parseInt(this.value))">
            </div>
            <div class="form-group">
                <label>Y Position:</label>
                <input type="number" id="field-y" value="${field.y}" onchange="formBuilder.updateField('y', parseInt(this.value))">
            </div>
            <div class="form-group">
                <label>Font Size:</label>
                <input type="number" id="field-size" value="${field.size}" min="6" max="72" onchange="formBuilder.updateField('size', parseInt(this.value))">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="field-bold" ${field.bold ? 'checked' : ''} onchange="formBuilder.updateField('bold', this.checked)">
                    Bold Text
                </label>
            </div>
            <div class="form-group">
                <label>Max Width (optional):</label>
                <input type="number" id="field-maxwidth" value="${field.maxWidth || ''}" onchange="formBuilder.updateField('maxWidth', this.value ? parseInt(this.value) : null)">
            </div>
            <div class="form-group">
                <button class="btn danger" onclick="formBuilder.deleteField('${field.id}')">🗑️ Delete Field</button>
            </div>
        `;
    }

    updateField(property, value) {
        if (!this.selectedField) return;

        const field = this.fields.get(this.selectedField);
        if (!field) return;

        field[property] = value;

        // Update marker position if coordinates changed
        if (property === 'x' || property === 'y') {
            this.updateFieldMarkerPosition(field);
        }

        // Update field label if name changed
        if (property === 'name') {
            const marker = document.getElementById(`marker_${field.id}`);
            if (marker) {
                const label = marker.querySelector('.field-label');
                if (label) {
                    label.textContent = value;
                }
            }
        }

        this.updateFieldList();
    }

    updateFieldMarkerPosition(field) {
        // Convert PDF coordinates back to canvas coordinates using zoom level
        const canvasX = field.x * this.zoom;
        const canvasY = (this.canvas.height / this.zoom - field.y) * this.zoom;

        field.canvasX = canvasX;
        field.canvasY = canvasY;

        const marker = document.getElementById(`marker_${field.id}`);
        if (marker) {
            // Calculate text height based on font size
            const textHeight = field.size * 0.8;

            // Position marker relative to container, accounting for canvas offset
            // X position stays at left edge, Y position adjusted to be at middle-left of text
            const markerX = this.pdfOffset.x + canvasX;
            const markerY = this.pdfOffset.y + canvasY - (textHeight / 2);

            marker.style.left = `${markerX}px`;
            marker.style.top = `${markerY}px`;
            marker.style.height = `${textHeight}px`;
        }

        console.log(`📍 Updated marker for ${field.name}: PDF(${field.x}, ${field.y}) → Canvas(${canvasX}, ${canvasY}) → Marker(${this.pdfOffset.x + canvasX}, ${this.pdfOffset.y + canvasY - (field.size * 0.4)}) [zoom: ${this.zoom}]`);
    }

    deleteField(fieldId) {
        this.fields.delete(fieldId);

        const marker = document.getElementById(`marker_${fieldId}`);
        if (marker) marker.remove();

        if (this.selectedField === fieldId) {
            this.selectedField = null;
            document.getElementById('field-editor').innerHTML = '<p style="color: #7f8c8d; font-style: italic;">No field selected</p>';
        }

        this.updateFieldList();
    }

    updateFieldList() {
        const list = document.getElementById('field-list');

        if (this.fields.size === 0) {
            list.innerHTML = '<div style="padding: 1rem; text-align: center; color: #7f8c8d;">No fields created yet</div>';
            return;
        }

        list.innerHTML = '';

        this.fields.forEach((field, id) => {
            const item = document.createElement('div');
            item.className = 'field-item';
            item.id = `item_${id}`;
            item.innerHTML = `
                <div class="field-name">${field.name}</div>
                <div class="field-coords">x: ${field.x}, y: ${field.y}, size: ${field.size}</div>
            `;

            item.addEventListener('click', () => this.selectField(id));
            list.appendChild(item);
        });
    }

    redrawFieldMarkers() {
        // Remove existing markers
        document.querySelectorAll('.field-marker').forEach(marker => marker.remove());

        // Recreate markers with updated positions
        this.fields.forEach(field => {
            // Update canvas coordinates based on current zoom and PDF coordinates
            const canvasX = field.x * this.zoom;
            const canvasY = (this.canvas.height / this.zoom - field.y) * this.zoom;

            field.canvasX = canvasX;
            field.canvasY = canvasY;

            // Create the marker with correct positioning
            this.createFieldMarker(field);
        });
    }

    drawGrid() {
        if (!this.ctx || !this.gridEnabled) return;

        const gridSize = 20 * this.pdfScale;

        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.lineWidth = 1;

        // Vertical lines
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        // Horizontal lines
        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }

        this.ctx.restore();
    }

    async loadConfig(file) {
        try {
            const text = await file.text();
            const config = JSON.parse(text);

            // Clear existing fields
            this.fields.clear();
            document.querySelectorAll('.field-marker').forEach(marker => marker.remove());

            // Load fields from config
            Object.entries(config).forEach(([name, fieldConfig]) => {
                const fieldId = `field_${Date.now()}_${Math.random()}`;

                // Convert PDF coordinates to canvas coordinates using zoom level
                const canvasX = fieldConfig.x * this.zoom;
                const canvasY = (this.canvas.height / this.zoom - fieldConfig.y) * this.zoom;

                const field = {
                    id: fieldId,
                    name: name,
                    x: fieldConfig.x,
                    y: fieldConfig.y,
                    canvasX: canvasX,
                    canvasY: canvasY,
                    size: fieldConfig.size || 10,
                    pageIndex: fieldConfig.pageIndex || 0,
                    bold: fieldConfig.bold || false,
                    maxWidth: fieldConfig.maxWidth || null,
                    lineSpacing: fieldConfig.lineSpacing || null,
                    maxLines: fieldConfig.maxLines || null,
                    type: fieldConfig.type || 'text'
                };

                this.fields.set(fieldId, field);
                this.createFieldMarker(field);
            });

            this.updateFieldList();
            this.showStatus('Configuration imported successfully!');

        } catch (error) {
            this.showStatus(`Error importing configuration: ${error.message}`, 'error');
        }
    }

    showStatus(message, type = 'success') {
        // Remove existing status
        const existingStatus = document.querySelector('.status');
        if (existingStatus) existingStatus.remove();

        const status = document.createElement('div');
        status.className = `status ${type === 'error' ? 'error' : ''}`;
        status.textContent = message;

        const sidebar = document.querySelector('.sidebar');
        sidebar.insertBefore(status, sidebar.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (status.parentNode) {
                status.remove();
            }
        }, 5000);
    }
}

// Global functions for HTML onclick handlers
let formBuilder;

function zoomIn() {
    if (!formBuilder) return;
    formBuilder.zoom = Math.min(formBuilder.zoom * 1.2, 3.0);
    formBuilder.renderPDF();
    document.getElementById('zoom-level').textContent = Math.round(formBuilder.zoom * 100) + '%';
}

function zoomOut() {
    if (!formBuilder) return;
    formBuilder.zoom = Math.max(formBuilder.zoom / 1.2, 0.5);
    formBuilder.renderPDF();
    document.getElementById('zoom-level').textContent = Math.round(formBuilder.zoom * 100) + '%';
}

function resetZoom() {
    if (!formBuilder) return;
    formBuilder.zoom = 1.0;
    formBuilder.renderPDF();
    document.getElementById('zoom-level').textContent = '100%';
}

function toggleGrid() {
    if (!formBuilder) return;
    formBuilder.gridEnabled = !formBuilder.gridEnabled;
    formBuilder.renderPDF();
}

function clearFields() {
    if (!formBuilder) return;
    if (confirm('Are you sure you want to clear all fields?')) {
        formBuilder.fields.clear();
        formBuilder.selectedField = null;
        document.querySelectorAll('.field-marker').forEach(marker => marker.remove());
        formBuilder.updateFieldList();
        document.getElementById('field-editor').innerHTML = '<p style="color: #7f8c8d; font-style: italic;">No field selected</p>';
    }
}

function clearFormData() {
    if (!formBuilder) return;
    clearFormTextOverlays();
    formBuilder.showStatus('Form data cleared from screen.', 'success');
}

async function downloadFilledPDF() {
    if (!formBuilder) return;

    if (formBuilder.fields.size === 0) {
        formBuilder.showStatus('No fields configured. Please create or load a configuration first.', 'error');
        return;
    }

    if (!formBuilder.pdfDoc) {
        formBuilder.showStatus('No PDF loaded. Please load a PDF first.', 'error');
        return;
    }

    // Show loading status
    formBuilder.showStatus('Generating PDF download with hardcoded test data...', 'success');

    try {
        // Use hardcoded test data (previously from resp-4-59.json)
        const jsonData = {
            "operation_report": {
                "date": "10/1/25",
                "time_commenced": "9:00 AM",
                "time_completed": "10:30 AM",
                "item_numbers": ["35633"],
                "medical_team": {
                    "surgeon": "Lee",
                    "assistant": "Dr. Smith",
                    "anaesthetist": "",
                    "other_staff": []
                },
                "operative_diagnosis": "Endocervical polyp",
                "operation_performed": "D&C, Hysteroscopy, Polypectomy",
                "operation_details": [
                    "Sterile procedure, Prep & drape",
                    "Cervix dilated, hysteroscopy - Womb, cavity, endometrium",
                    "No endometrial polyp",
                    "Endocervical polyp seen, polypectomy performed",
                    "Curettings obtained, repeat hysteroscopy - all normal"
                ],
                "vaginal_pack": false,
                "specimen": {
                    "pathology": true,
                    "discard": false,
                    "description": "Endocervical polyp tissue",
                    "laboratory": "Melb Path"
                }
            },
            "post_procedure_orders": {
                "routine_observations": ["RPAO"],
                "position_in_bed": "Semi-fowler position",
                "dressings": "Perineal pad as required",
                "drug_and_iv_therapy": "Paracetamol 1g QID PRN",
                "drain_tubes": {
                    "present": false,
                    "type": ""
                }
            }
        };

        // Generate PDF client-side using canvas
        await generateClientSidePDF(jsonData);

        formBuilder.showStatus('PDF filled and downloaded successfully!', 'success');

    } catch (error) {
        console.error('Error downloading filled PDF:', error);
        formBuilder.showStatus(`Error downloading PDF: ${error.message}`, 'error');
    }
}

async function generateClientSidePDF(jsonData) {
    try {
        // Check if jsPDF is available
        if (typeof window.jsPDF === 'undefined') {
            throw new Error('jsPDF library not loaded. Please refresh the page.');
        }

        // Create a new canvas to render the filled PDF
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        // Set canvas dimensions to match the original PDF
        tempCanvas.width = formBuilder.canvas.width;
        tempCanvas.height = formBuilder.canvas.height;

        // Draw the original PDF onto the temporary canvas
        tempCtx.drawImage(formBuilder.canvas, 0, 0);

        // Save the context state
        tempCtx.save();

        // Draw all the form fields with their data
        formBuilder.fields.forEach(field => {
            const fieldValue = getFieldValue(jsonData, field.name);

            if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
                drawTextOnTempCanvas(tempCtx, field, fieldValue);
            }
        });

        // Restore context state
        tempCtx.restore();

        // Convert canvas to image data URL
        const imgDataUrl = tempCanvas.toDataURL('image/jpeg', 0.95);

        // Create PDF using jsPDF
        const { jsPDF } = window.jsPDF;

        // Calculate PDF dimensions based on canvas size
        // Standard PDF page is 8.5" x 11" (612 x 792 points)
        // We'll scale the canvas to fit the page while maintaining aspect ratio
        const canvasAspectRatio = tempCanvas.width / tempCanvas.height;
        const pageWidth = 612; // 8.5 inches in points
        const pageHeight = 792; // 11 inches in points
        const pageAspectRatio = pageWidth / pageHeight;

        let imgWidth, imgHeight;
        if (canvasAspectRatio > pageAspectRatio) {
            // Canvas is wider, fit to width
            imgWidth = pageWidth;
            imgHeight = pageWidth / canvasAspectRatio;
        } else {
            // Canvas is taller, fit to height
            imgHeight = pageHeight;
            imgWidth = pageHeight * canvasAspectRatio;
        }

        // Center the image on the page
        const x = (pageWidth - imgWidth) / 2;
        const y = (pageHeight - imgHeight) / 2;

        // Create PDF document
        const pdf = new jsPDF({
            orientation: imgHeight > imgWidth ? 'portrait' : 'landscape',
            unit: 'pt',
            format: [pageWidth, pageHeight]
        });

        // Add the image to PDF
        pdf.addImage(imgDataUrl, 'JPEG', x, y, imgWidth, imgHeight);

        // Download the PDF
        const filename = `filled-surgical-report-${new Date().toISOString().slice(0, 10)}.pdf`;
        pdf.save(filename);

        formBuilder.showStatus('PDF downloaded successfully!', 'success');

    } catch (error) {
        console.error('Error generating client-side PDF:', error);
        throw error;
    }
}

function drawTextOnTempCanvas(ctx, field, text) {
    // Convert PDF coordinates to canvas coordinates (same logic as drawTextOnCanvas)
    const canvasX = field.x * formBuilder.zoom;
    const canvasY = (formBuilder.canvas.height / formBuilder.zoom - field.y) * formBuilder.zoom;

    // Set font properties using Microsoft Sans Serif (user preference)
    const fontSize = field.size * formBuilder.zoom;
    const fontWeight = field.bold ? 'bold' : 'normal';
    ctx.font = `${fontWeight} ${fontSize}px "Microsoft Sans Serif", Arial, sans-serif`;
    ctx.fillStyle = '#000000'; // Black color for final PDF

    // Set baseline to middle to align with marker's middle-left positioning
    ctx.textBaseline = 'middle';

    const adjustedCanvasY = canvasY;

    // Handle multi-line text
    if (text.includes('\n')) {
        const lines = text.split('\n');
        const lineHeight = fontSize * 1.2;

        lines.forEach((line, index) => {
            if (field.maxLines && index >= field.maxLines) return;

            let displayText = line;

            // Handle max width by truncating if necessary
            if (field.maxWidth) {
                const maxWidthCanvas = field.maxWidth * formBuilder.zoom;
                while (ctx.measureText(displayText).width > maxWidthCanvas && displayText.length > 0) {
                    displayText = displayText.slice(0, -1);
                }
                if (displayText !== line) {
                    displayText += '...';
                }
            }

            const lineY = adjustedCanvasY + (index * lineHeight);
            ctx.fillText(displayText, canvasX, lineY);
        });
    } else {
        let displayText = text;

        // Handle max width by truncating if necessary
        if (field.maxWidth) {
            const maxWidthCanvas = field.maxWidth * formBuilder.zoom;
            while (ctx.measureText(displayText).width > maxWidthCanvas && displayText.length > 0) {
                displayText = displayText.slice(0, -1);
            }
            if (displayText !== text) {
                displayText += '...';
            }
        }

        ctx.fillText(displayText, canvasX, adjustedCanvasY);
    }
}

function loadDefaultPDF() {
    if (!formBuilder) return;
    formBuilder.loadDefaultPDF();
}

function exportConfig() {
    if (!formBuilder) return;
    const config = {};

    formBuilder.fields.forEach(field => {
        config[field.name] = {
            x: field.x,
            y: field.y,
            size: field.size,
            pageIndex: field.pageIndex,
            bold: field.bold
        };

        if (field.maxWidth) config[field.name].maxWidth = field.maxWidth;
        if (field.lineSpacing) config[field.name].lineSpacing = field.lineSpacing;
        if (field.maxLines) config[field.name].maxLines = field.maxLines;
    });

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'form-config.json';
    a.click();
    URL.revokeObjectURL(url);

    formBuilder.showStatus('Configuration exported successfully!');
}

function importConfig() {
    document.getElementById('config-file').click();
}

function loadExistingConfig() {
    // Load the existing form-config.js configuration
    fetch('./form-config.js')
        .then(response => response.text())
        .then(text => {
            // Extract the surgicalReportConfig object
            const configMatch = text.match(/export const surgicalReportConfig = ({[\s\S]*?});/);
            if (configMatch) {
                // Convert the JavaScript object to JSON
                const configStr = configMatch[1]
                    .replace(/'/g, '"')
                    .replace(/(\w+):/g, '"$1":')
                    .replace(/,\s*}/g, '}');

                try {
                    const config = JSON.parse(configStr);

                    // Clear existing fields
                    formBuilder.fields.clear();
                    document.querySelectorAll('.field-marker').forEach(marker => marker.remove());

                    // Load fields from config
                    Object.entries(config).forEach(([name, fieldConfig]) => {
                        const fieldId = `field_${Date.now()}_${Math.random()}`;

                        // Convert PDF coordinates to canvas coordinates using zoom level
                        const canvasX = fieldConfig.x * formBuilder.zoom;
                        const canvasY = (formBuilder.canvas.height / formBuilder.zoom - fieldConfig.y) * formBuilder.zoom;

                        const field = {
                            id: fieldId,
                            name: name,
                            x: fieldConfig.x,
                            y: fieldConfig.y,
                            canvasX: canvasX,
                            canvasY: canvasY,
                            size: fieldConfig.size || 10,
                            pageIndex: fieldConfig.pageIndex || 0,
                            bold: fieldConfig.bold || false,
                            maxWidth: fieldConfig.maxWidth || null,
                            lineSpacing: fieldConfig.lineSpacing || null,
                            maxLines: fieldConfig.maxLines || null,
                            type: fieldConfig.type || 'text'
                        };

                        formBuilder.fields.set(fieldId, field);
                        formBuilder.createFieldMarker(field);
                    });

                    formBuilder.updateFieldList();
                    formBuilder.showStatus('Existing configuration loaded successfully!');

                } catch (error) {
                    formBuilder.showStatus(`Error parsing existing config: ${error.message}`, 'error');
                }
            } else {
                formBuilder.showStatus('Could not find configuration in form-config.js', 'error');
            }
        })
        .catch(error => {
            formBuilder.showStatus(`Error loading existing config: ${error.message}`, 'error');
        });
}

function testForm() {
    if (!formBuilder) return;
    if (formBuilder.fields.size === 0) {
        formBuilder.showStatus('No fields to test. Create some fields first.', 'error');
        return;
    }

    // Create a test modal
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;

    // Generate test form
    let formHTML = `
        <h2>🧪 Test Form Configuration</h2>
        <p>Enter test data to see how the form will be filled:</p>
        <form id="test-form" style="margin: 1rem 0;">
    `;

    // Sample data from resp-4-59.json
    const sampleData = {
        'operation_report.date': '10/1/25',
        'operation_report.time_commenced': '9:00 AM',
        'operation_report.time_completed': '10:30 AM',
        'operation_report.item_numbers': '35633',
        'operation_report.medical_team.surgeon': 'Lee',
        'operation_report.medical_team.assistant': 'Dr. Smith',
        'operation_report.operative_diagnosis': 'Endocervical polyp',
        'operation_report.operation_performed': 'D&C, Hysteroscopy, Polypectomy',
        'operation_report.operation_details': 'Sterile procedure, Prep & drape\nCervix dilated, hysteroscopy - Womb, cavity, endometrium\nNo endometrial polyp\nEndocervical polyp seen, polypectomy performed\nCurettings obtained, repeat hysteroscopy - all normal',
        'operation_report.vaginal_pack': 'false',
        'operation_report.specimen.pathology': 'true',
        'operation_report.specimen.discard': 'false',
        'operation_report.specimen.description': 'Endocervical polyp tissue',
        'operation_report.specimen.laboratory': 'Melb Path',
        'post_procedure_orders.routine_observations': 'RPAO',
        'post_procedure_orders.position_in_bed': 'Semi-fowler position',
        'post_procedure_orders.dressings': 'Perineal pad as required',
        'post_procedure_orders.drug_and_iv_therapy': 'Paracetamol 1g QID PRN',
        'post_procedure_orders.drain_tubes.present': 'false',
        'post_procedure_orders.drain_tubes.type': ''
    };

    formBuilder.fields.forEach(field => {
        const sampleValue = sampleData[field.name] || '';
        const inputType = field.type === 'array' ? 'textarea' : 'text';
        const inputElement = field.type === 'array'
            ? `<textarea name="${field.name}" rows="3" placeholder="Enter values for ${field.name} (one per line)" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">${sampleValue}</textarea>`
            : `<input type="text" name="${field.name}" value="${sampleValue}" placeholder="Enter value for ${field.name}" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">`;

        formHTML += `
            <div style="margin-bottom: 1rem;">
                <label style="display: block; font-weight: bold; margin-bottom: 0.25rem;">
                    ${field.name}:
                    <span style="font-weight: normal; color: #666; font-size: 0.8em;">(${field.type})</span>
                </label>
                ${inputElement}
                <small style="color: #666;">Position: x=${field.x}, y=${field.y}, size=${field.size}</small>
            </div>
        `;
    });

    formHTML += `
        </form>
        <div style="margin-top: 1.5rem; display: flex; gap: 1rem;">
            <button onclick="generateTestConfig()" style="background: #27ae60; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
                📋 Generate Config
            </button>
            <button onclick="downloadTestData()" style="background: #3498db; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
                💾 Download Test Data
            </button>
            <button onclick="closeTestModal()" style="background: #95a5a6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
                ❌ Close
            </button>
        </div>
        <div id="test-output" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4>Generated Configuration:</h4>
            <pre id="config-output" style="background: #2c3e50; color: #ecf0f1; padding: 1rem; border-radius: 4px; overflow-x: auto; font-size: 0.8rem;"></pre>
        </div>
    `;

    content.innerHTML = formHTML;
    modal.appendChild(content);
    document.body.appendChild(modal);

    // Store modal reference for cleanup
    window.testModal = modal;
}

function generateTestConfig() {
    if (!formBuilder) return;
    const config = {};
    formBuilder.fields.forEach(field => {
        config[field.name] = {
            x: field.x,
            y: field.y,
            size: field.size,
            pageIndex: field.pageIndex,
            bold: field.bold
        };

        if (field.maxWidth) config[field.name].maxWidth = field.maxWidth;
        if (field.lineSpacing) config[field.name].lineSpacing = field.lineSpacing;
        if (field.maxLines) config[field.name].maxLines = field.maxLines;
    });

    const output = document.getElementById('test-output');
    const configOutput = document.getElementById('config-output');

    configOutput.textContent = JSON.stringify(config, null, 2);
    output.style.display = 'block';
}

function downloadTestData() {
    const form = document.getElementById('test-form');
    const formData = new FormData(form);
    const testData = {};

    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            testData[key] = value;
        }
    }

    const blob = new Blob([JSON.stringify(testData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'test-data.json';
    a.click();
    URL.revokeObjectURL(url);
}

function closeTestModal() {
    if (window.testModal) {
        window.testModal.remove();
        window.testModal = null;
    }
}

function fillOutForm() {
    if (!formBuilder) return;

    if (formBuilder.fields.size === 0) {
        formBuilder.showStatus('No fields configured. Please create or load a configuration first.', 'error');
        return;
    }

    if (!formBuilder.pdfDoc) {
        formBuilder.showStatus('No PDF loaded. Please load a PDF first.', 'error');
        return;
    }

    // Show loading status
    formBuilder.showStatus('Filling form with hardcoded test data on screen...', 'success');

    // Hardcoded test data (previously from resp-4-59.json)
    const hardcodedData = {
        "operation_report": {
            "date": "10/1/25",
            "time_commenced": "9:00 AM",
            "time_completed": "10:30 AM",
            "item_numbers": ["35633"],
            "medical_team": {
                "surgeon": "Lee",
                "assistant": "Dr. Smith",
                "anaesthetist": "",
                "other_staff": []
            },
            "operative_diagnosis": "Endocervical polyp",
            "operation_performed": "D&C, Hysteroscopy, Polypectomy",
            "operation_details": [
                "Sterile procedure, Prep & drape",
                "Cervix dilated, hysteroscopy - Womb, cavity, endometrium",
                "No endometrial polyp",
                "Endocervical polyp seen, polypectomy performed",
                "Curettings obtained, repeat hysteroscopy - all normal"
            ],
            "vaginal_pack": false,
            "specimen": {
                "pathology": true,
                "discard": false,
                "description": "Endocervical polyp tissue",
                "laboratory": "Melb Path"
            }
        },
        "post_procedure_orders": {
            "routine_observations": ["RPAO"],
            "position_in_bed": "Semi-fowler position",
            "dressings": "Perineal pad as required",
            "drug_and_iv_therapy": "Paracetamol 1g QID PRN",
            "drain_tubes": {
                "present": false,
                "type": ""
            }
        }
    };

    // Display the hardcoded data on screen
    displayFilledFormOnScreen(hardcodedData);
}

function displayFilledFormOnScreen(jsonData) {
    try {
        // Get the canvas context for drawing text
        const ctx = formBuilder.ctx;
        if (!ctx) {
            throw new Error('Canvas context not available');
        }

        console.log('🔍 Debug: Starting form fill process');
        console.log('🔍 Debug: Number of fields:', formBuilder.fields.size);
        console.log('🔍 Debug: Canvas dimensions:', formBuilder.canvas.width, 'x', formBuilder.canvas.height);
        console.log('🔍 Debug: Current zoom:', formBuilder.zoom);

        // Save the current canvas state
        ctx.save();

        let fieldsProcessed = 0;
        let fieldsDrawn = 0;

        // Process each field and display the corresponding data
        formBuilder.fields.forEach(field => {
            fieldsProcessed++;
            console.log(`🔍 Debug: Processing field ${fieldsProcessed}: ${field.name}`);

            const fieldValue = getFieldValue(jsonData, field.name);
            console.log(`🔍 Debug: Field value for ${field.name}:`, fieldValue);

            if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
                fieldsDrawn++;
                console.log(`🔍 Debug: Drawing field ${field.name} with value: "${fieldValue}"`);
                drawTextOnCanvas(ctx, field, fieldValue);
            } else {
                console.log(`🔍 Debug: Skipping field ${field.name} - no valid value`);
            }
        });

        // Restore canvas state
        ctx.restore();

        console.log(`🔍 Debug: Processed ${fieldsProcessed} fields, drew ${fieldsDrawn} fields`);
        formBuilder.showStatus(`Form filled with test data! Processed ${fieldsProcessed} fields, drew ${fieldsDrawn} on screen.`, 'success');
        console.log('📝 Form filled on screen with data:', jsonData);

    } catch (error) {
        console.error('Error displaying filled form:', error);
        formBuilder.showStatus(`Error displaying filled form: ${error.message}`, 'error');
    }
}

function getFieldValue(jsonData, fieldPath) {
    // Navigate through nested object using dot notation
    const pathParts = fieldPath.split('.');
    let value = jsonData;

    for (const part of pathParts) {
        if (value && typeof value === 'object' && part in value) {
            value = value[part];
        } else {
            return null; // Path not found
        }
    }

    // Handle different data types
    if (Array.isArray(value)) {
        return value.join('\n'); // Join array items with newlines
    } else if (typeof value === 'boolean') {
        return value ? 'X' : ''; // Show 'X' for true, empty for false
    } else {
        return String(value);
    }
}

function drawTextOnCanvas(ctx, field, text) {
    // Convert PDF coordinates to canvas coordinates
    const canvasX = field.x * formBuilder.zoom;
    const canvasY = (formBuilder.canvas.height / formBuilder.zoom - field.y) * formBuilder.zoom;

    // Set font properties
    const fontSize = field.size * formBuilder.zoom;
    const fontWeight = field.bold ? 'bold' : 'normal';
    ctx.font = `${fontWeight} ${fontSize}px Arial, sans-serif`;
    ctx.fillStyle = '#000080'; // Blue color to distinguish from PDF content

    // Set baseline to middle to align with marker's middle-left positioning
    // The marker is positioned at the middle-left of the text height
    ctx.textBaseline = 'middle';

    // Adjust Y position: marker is positioned at middle-left of text,
    // so we need to draw text at the same Y position as the marker represents
    const adjustedCanvasY = canvasY;

    // Handle multi-line text
    if (text.includes('\n')) {
        const lines = text.split('\n');
        const lineHeight = fontSize * 1.2;

        lines.forEach((line, index) => {
            if (field.maxLines && index >= field.maxLines) return; // Respect max lines

            let displayText = line;

            // Handle max width by truncating if necessary
            if (field.maxWidth) {
                const maxWidthCanvas = field.maxWidth * formBuilder.zoom;
                while (ctx.measureText(displayText).width > maxWidthCanvas && displayText.length > 0) {
                    displayText = displayText.slice(0, -1);
                }
                if (displayText !== line) {
                    displayText += '...'; // Add ellipsis for truncated text
                }
            }

            // For multi-line text, adjust each line's Y position relative to the first line
            // The first line should be at the marker position, subsequent lines below
            const lineY = adjustedCanvasY + (index * lineHeight);
            ctx.fillText(displayText, canvasX, lineY);
        });
    } else {
        let displayText = text;

        // Handle max width by truncating if necessary
        if (field.maxWidth) {
            const maxWidthCanvas = field.maxWidth * formBuilder.zoom;
            while (ctx.measureText(displayText).width > maxWidthCanvas && displayText.length > 0) {
                displayText = displayText.slice(0, -1);
            }
            if (displayText !== text) {
                displayText += '...'; // Add ellipsis for truncated text
            }
        }

        ctx.fillText(displayText, canvasX, adjustedCanvasY);
    }

    console.log(`📝 Drew text "${text.substring(0, 20)}..." at canvas(${canvasX}, ${adjustedCanvasY}) for field ${field.name} (marker-aligned)`);
}

function clearFormTextOverlays() {
    // Re-render the PDF to clear any previous text overlays
    if (formBuilder.pdfDoc) {
        formBuilder.renderPDF();
    }
}

function loadTestConfig() {
    if (!formBuilder) return;
    // Create test configuration based on resp-4-59.json structure
    const testConfig = {
        'operation_report.date': {
            x: 150,
            y: 750,
            size: 11,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.time_commenced': {
            x: 300,
            y: 750,
            size: 11,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.time_completed': {
            x: 450,
            y: 750,
            size: 11,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.item_numbers': {
            x: 150,
            y: 720,
            size: 11,
            pageIndex: 0,
            type: 'array'
        },
        'operation_report.medical_team.surgeon': {
            x: 150,
            y: 690,
            size: 11,
            bold: true,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.medical_team.assistant': {
            x: 350,
            y: 690,
            size: 11,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.operative_diagnosis': {
            x: 150,
            y: 660,
            size: 11,
            maxWidth: 400,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.operation_performed': {
            x: 150,
            y: 630,
            size: 11,
            maxWidth: 400,
            bold: true,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.operation_details': {
            x: 80,
            y: 580,
            size: 10,
            maxWidth: 450,
            lineSpacing: 15,
            maxLines: 20,
            pageIndex: 0,
            type: 'array'
        },
        'operation_report.vaginal_pack': {
            x: 400,
            y: 200,
            size: 12,
            pageIndex: 0,
            type: 'checkbox'
        },
        'operation_report.specimen.pathology': {
            x: 200,
            y: 200,
            size: 12,
            pageIndex: 0,
            type: 'checkbox'
        },
        'operation_report.specimen.discard': {
            x: 300,
            y: 200,
            size: 12,
            pageIndex: 0,
            type: 'checkbox'
        },
        'operation_report.specimen.description': {
            x: 150,
            y: 170,
            size: 10,
            maxWidth: 300,
            pageIndex: 0,
            type: 'text'
        },
        'operation_report.specimen.laboratory': {
            x: 150,
            y: 140,
            size: 10,
            pageIndex: 0,
            type: 'text'
        },
        'post_procedure_orders.routine_observations': {
            x: 80,
            y: 300,
            size: 10,
            maxWidth: 450,
            lineSpacing: 12,
            maxLines: 10,
            pageIndex: 0,
            type: 'array'
        },
        'post_procedure_orders.position_in_bed': {
            x: 150,
            y: 250,
            size: 10,
            maxWidth: 300,
            pageIndex: 0,
            type: 'text'
        },
        'post_procedure_orders.dressings': {
            x: 150,
            y: 220,
            size: 10,
            maxWidth: 300,
            pageIndex: 0,
            type: 'text'
        },
        'post_procedure_orders.drug_and_iv_therapy': {
            x: 150,
            y: 190,
            size: 10,
            maxWidth: 300,
            pageIndex: 0,
            type: 'text'
        },
        'post_procedure_orders.drain_tubes.present': {
            x: 200,
            y: 160,
            size: 12,
            pageIndex: 0,
            type: 'checkbox'
        },
        'post_procedure_orders.drain_tubes.type': {
            x: 250,
            y: 160,
            size: 10,
            maxWidth: 200,
            pageIndex: 0,
            type: 'text'
        }
    };

    if (!formBuilder.canvas) {
        formBuilder.showStatus('Please load a PDF first before loading test configuration.', 'error');
        return;
    }

    // Clear existing fields
    formBuilder.fields.clear();
    document.querySelectorAll('.field-marker').forEach(marker => marker.remove());

    // Load fields from test config
    Object.entries(testConfig).forEach(([name, fieldConfig]) => {
        const fieldId = `field_${Date.now()}_${Math.random()}`;

        // Convert PDF coordinates to canvas coordinates using zoom level
        const canvasX = fieldConfig.x * formBuilder.zoom;
        const canvasY = (formBuilder.canvas.height / formBuilder.zoom - fieldConfig.y) * formBuilder.zoom;

        const field = {
            id: fieldId,
            name: name,
            x: fieldConfig.x,
            y: fieldConfig.y,
            canvasX: canvasX,
            canvasY: canvasY,
            size: fieldConfig.size || 10,
            pageIndex: fieldConfig.pageIndex || 0,
            bold: fieldConfig.bold || false,
            maxWidth: fieldConfig.maxWidth || null,
            lineSpacing: fieldConfig.lineSpacing || null,
            maxLines: fieldConfig.maxLines || null,
            type: fieldConfig.type || 'text'
        };

        formBuilder.fields.set(fieldId, field);
        formBuilder.createFieldMarker(field);
    });

    formBuilder.updateFieldList();
    formBuilder.showStatus(`Test configuration loaded! ${Object.keys(testConfig).length} fields created based on resp-4-59.json structure.`);
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    formBuilder = new PDFFormBuilder();
});



