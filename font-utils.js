/**
 * Font Utilities for loading fonts from CDN sources
 * Specifically designed to find Microsoft Sans Serif alternatives from reliable CDN sources
 */

/**
 * CDN font configurations with multiple fallback sources
 * Each font has multiple CDN URLs for redundancy
 */
const CDN_FONT_CONFIGS = {
  'open-sans': {
    name: 'Open Sans',
    description: 'Similar to Microsoft Sans Serif, clean and readable',
    regular: [
      'https://fonts.gstatic.com/s/opensans/v35/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVc.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/open-sans@4.5.14/files/open-sans-latin-400-normal.ttf',
      'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400&display=swap'
    ],
    bold: [
      'https://fonts.gstatic.com/s/opensans/v35/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4taVc.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/open-sans@4.5.14/files/open-sans-latin-700-normal.ttf'
    ]
  },
  'liberation-sans': {
    name: 'Liberation Sans',
    description: 'Open source alternative to Arial/Microsoft Sans Serif',
    regular: [
      'https://cdn.jsdelivr.net/npm/@fontsource/liberation-sans@4.5.4/files/liberation-sans-latin-400-normal.ttf',
      'https://github.com/liberationfonts/liberation-fonts/raw/main/liberation-fonts-ttf-2.1.5/LiberationSans-Regular.ttf'
    ],
    bold: [
      'https://cdn.jsdelivr.net/npm/@fontsource/liberation-sans@4.5.4/files/liberation-sans-latin-700-normal.ttf',
      'https://github.com/liberationfonts/liberation-fonts/raw/main/liberation-fonts-ttf-2.1.5/LiberationSans-Bold.ttf'
    ]
  },
  'roboto': {
    name: 'Roboto',
    description: 'Modern sans-serif font, good alternative to Microsoft Sans Serif',
    regular: [
      'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7GxKOzY.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/roboto@4.5.8/files/roboto-latin-400-normal.ttf'
    ],
    bold: [
      'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/roboto@4.5.8/files/roboto-latin-700-normal.ttf'
    ]
  },
  'inter': {
    name: 'Inter',
    description: 'Highly readable font optimized for user interfaces',
    regular: [
      'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/inter@4.5.15/files/inter-latin-400-normal.ttf'
    ],
    bold: [
      'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.ttf',
      'https://cdn.jsdelivr.net/npm/@fontsource/inter@4.5.15/files/inter-latin-700-normal.ttf'
    ]
  }
};

/**
 * Font priority order - which fonts to try first
 * Based on similarity to Microsoft Sans Serif and reliability
 */
const FONT_PRIORITY = [
  'open-sans',      // Most similar to Microsoft Sans Serif
  'liberation-sans', // Open source alternative to Arial/MS Sans Serif
  'roboto',         // Popular, reliable fallback
  'inter'           // Modern, highly readable
];

/**
 * Fetch font from CDN URL with retry logic
 * @param {string} url - Font URL to fetch
 * @param {number} timeout - Request timeout in milliseconds
 * @returns {Promise<ArrayBuffer|null>} Font bytes or null if failed
 */
export async function fetchFontFromCDN(url, timeout = 10000) {
  try {
    console.log(`🌐 Fetching font from: ${url}`);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && !contentType.includes('font') && !contentType.includes('octet-stream')) {
      console.log(`⚠️  Unexpected content type: ${contentType}`);
    }

    const fontBytes = await response.arrayBuffer();
    console.log(`✓ Font loaded: ${fontBytes.byteLength} bytes`);
    return fontBytes;

  } catch (error) {
    console.log(`❌ Failed to fetch font from ${url}: ${error.message}`);
    return null;
  }
}

/**
 * Try to load font from multiple CDN URLs with fallback
 * @param {Array<string>} urls - Array of font URLs to try
 * @param {number} timeout - Request timeout per URL
 * @returns {Promise<ArrayBuffer|null>} Font bytes or null if all failed
 */
export async function loadFontWithFallback(urls, timeout = 10000) {
  for (const url of urls) {
    const fontBytes = await fetchFontFromCDN(url, timeout);
    if (fontBytes) {
      return fontBytes;
    }
  }
  return null;
}

/**
 * Get the best available CDN font configuration
 * @returns {Object} Font configuration with CDN URLs
 */
export function getBestCDNFont() {
  // Return the first font in priority order (Open Sans - most similar to Microsoft Sans Serif)
  const fontKey = FONT_PRIORITY[0];
  const fontConfig = CDN_FONT_CONFIGS[fontKey];

  return {
    name: fontConfig.name,
    description: fontConfig.description,
    regularUrls: fontConfig.regular,
    boldUrls: fontConfig.bold,
    key: fontKey
  };
}

/**
 * Get CDN font information and recommendations
 * @returns {Object} Font information object
 */
export function getFontInfo() {
  const bestFont = getBestCDNFont();

  return {
    platform: 'CDN',
    bestFont: {
      name: bestFont.name,
      regular: bestFont.regularUrls[0], // Primary URL
      bold: bestFont.boldUrls[0],       // Primary URL
      description: bestFont.description
    },
    recommendations: {
      'CDN': 'Using CDN fonts for cross-platform compatibility',
      'fallback': 'Will fallback to Helvetica if CDN fonts fail to load'
    },
    availableFonts: FONT_PRIORITY.map(key => ({
      key,
      name: CDN_FONT_CONFIGS[key].name,
      description: CDN_FONT_CONFIGS[key].description
    }))
  };
}

/**
 * Create a font configuration for PDF form filler using CDN fonts
 * @returns {Object} Font configuration
 */
export function createFontConfig() {
  const bestFont = getBestCDNFont();

  return {
    hasCustomFont: true, // CDN fonts are always available
    fontName: bestFont.name,
    regularUrls: bestFont.regularUrls,
    boldUrls: bestFont.boldUrls,
    fallbackToStandard: false, // We'll handle fallback in the loading logic
    useCDN: true,
    description: bestFont.description
  };
}

/**
 * Get all available CDN font options
 * @returns {Array} Array of available font configurations
 */
export function getAvailableCDNFonts() {
  return FONT_PRIORITY.map(key => ({
    key,
    name: CDN_FONT_CONFIGS[key].name,
    description: CDN_FONT_CONFIGS[key].description,
    regularUrls: CDN_FONT_CONFIGS[key].regular,
    boldUrls: CDN_FONT_CONFIGS[key].bold
  }));
}

/**
 * Get specific CDN font configuration by key
 * @param {string} fontKey - Font key from FONT_PRIORITY
 * @returns {Object|null} Font configuration or null if not found
 */
export function getCDNFontConfig(fontKey) {
  const fontConfig = CDN_FONT_CONFIGS[fontKey];
  if (!fontConfig) {
    return null;
  }

  return {
    key: fontKey,
    name: fontConfig.name,
    description: fontConfig.description,
    regularUrls: fontConfig.regular,
    boldUrls: fontConfig.bold
  };
}

/**
 * Validate font URL by attempting to fetch headers
 * @param {string} fontUrl - URL to font file
 * @returns {Promise<boolean>} True if font URL is accessible
 */
export async function validateFontUrl(fontUrl) {
  try {
    const response = await fetch(fontUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * List all available CDN fonts
 * @returns {Array} Array of CDN font configurations
 */
export function listCDNFonts() {
  return getAvailableCDNFonts();
}

/**
 * Test CDN font connectivity
 * @returns {Promise<Object>} Test results for all CDN fonts
 */
export async function testCDNFontConnectivity() {
  const results = {
    timestamp: new Date().toISOString(),
    fonts: []
  };

  for (const fontKey of FONT_PRIORITY) {
    const fontConfig = CDN_FONT_CONFIGS[fontKey];
    const fontResult = {
      key: fontKey,
      name: fontConfig.name,
      regular: { available: [], failed: [] },
      bold: { available: [], failed: [] }
    };

    // Test regular font URLs
    for (const url of fontConfig.regular) {
      const isAvailable = await validateFontUrl(url);
      if (isAvailable) {
        fontResult.regular.available.push(url);
      } else {
        fontResult.regular.failed.push(url);
      }
    }

    // Test bold font URLs
    for (const url of fontConfig.bold) {
      const isAvailable = await validateFontUrl(url);
      if (isAvailable) {
        fontResult.bold.available.push(url);
      } else {
        fontResult.bold.failed.push(url);
      }
    }

    results.fonts.push(fontResult);
  }

  return results;
}

export default {
  fetchFontFromCDN,
  loadFontWithFallback,
  getBestCDNFont,
  getFontInfo,
  createFontConfig,
  getAvailableCDNFonts,
  getCDNFontConfig,
  validateFontUrl,
  listCDNFonts,
  testCDNFontConnectivity
};
